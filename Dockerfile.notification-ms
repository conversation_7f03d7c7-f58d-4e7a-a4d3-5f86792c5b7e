FROM node:20-alpine as builder

WORKDIR /usr/src/app

COPY package*.json ./
COPY apps/notification-ms/package.json apps/notification-ms/
COPY tsconfig*.json ./
COPY nest-cli.json ./

RUN mkdir -p apps

COPY apps/notification-ms apps/notification-ms
COPY libs libs

RUN npm install
RUN npm run build notification-ms

FROM node:20-alpine

ARG NODE_ENV=production
ENV NODE_ENV=${NODE_ENV}
ENV TZ=UTC

WORKDIR /usr/src/app
# Install logrotate + timezone data
RUN apk add --no-cache logrotate tzdata

# Create logs directory
RUN mkdir -p /usr/src/app/logs

# Copy logrotate configuration
COPY logrotate.conf /etc/logrotate.d/applogs

# Add cron job for daily rotation
RUN echo "0 0 * * * /usr/sbin/logrotate -f /etc/logrotate.d/applogs" >> /etc/crontabs/root

# Start cron in the background
RUN crond

# Copy package.json and install production dependencies
COPY package*.json ./
COPY apps/notification-ms/package.json apps/notification-ms/

RUN npm install --production

# Copy the built application from the builder stage
COPY --from=builder /usr/src/app/dist ./dist

EXPOSE 3000
CMD ["node", "dist/apps/notification-ms/main"]  
