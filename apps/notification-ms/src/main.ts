import { NestFactory } from '@nestjs/core';
import { NotificationMsModule } from './notification-ms.module';
import { ConfigService } from '@nestjs/config';
import { Logger } from 'nestjs-pino';
import { Transport } from '@nestjs/microservices';
import { QueueName } from '@app/shared/util/QueueName';

async function bootstrap() {
  const appM = await NestFactory.create(NotificationMsModule);
  const configService = appM.get(ConfigService);

  appM.useLogger(appM.get(Logger));
  appM.connectMicroservice({
    transport: Transport.RMQ,
    options: {
      urls: [configService.get<string>('RABBITMQ_URI')],
      queue: QueueName.NOTIFICATION_QUEUE,
      queueOptions: {
        durable: true,
      },
      noAck: false,
      prefetchCount: 1,
    },
  });

  appM.connectMicroservice({
    transport: Transport.TCP,
     { inheritAppConfig: true },
  );

  await appM.startAllMicroservices();
}
bootstrap();
