import { NestFactory } from '@nestjs/core';
import { UserServiceModule } from './user-service.module';
import { ConfigService } from '@nestjs/config';
import { Logger } from 'nestjs-pino';
import { Transport } from '@nestjs/microservices';

async function bootstrap() {
  const appM = await NestFactory.create(UserServiceModule);
  const configService = appM.get(ConfigService);

  appM.useLogger(appM.get(Logger));
  console.log('USER_SERVICE_PORT', configService.get('USER_SERVICE_PORT'));
  const app = await appM.connectMicroservice({
    transport: Transport.TCP,
    options: {
      port: configService.get('USER_SERVICE_PORT'),
      host: '0.0.0.0',
    },
  });

  await appM.startAllMicroservices();

  await app.listen();
}
bootstrap();
