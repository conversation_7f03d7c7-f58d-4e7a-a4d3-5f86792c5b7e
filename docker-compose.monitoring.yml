services:
  aptitest-grafana:
    image: grafana/grafana:latest
    container_name: aptitest-grafana
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_ADMIN_PASSWORD}
      - GF_SECURITY_ADMIN_USER=${GRAFANA_ADMIN_USER}
    ports:
      - 3050:3000
    networks:
      - microservices-network
    depends_on:
      - aptitest-prometheus  

  node-exporter:
    image: prom/node-exporter:latest
    container_name: node-exporter
    ports:
      - 9100:9100
    networks:
      - microservices-network    

  aptitest-prometheus:
    image: prom/prometheus:latest
    container_name: aptitest-prometheus
    ports:
      - 9090:9090
    networks:
      - microservices-network
    volumes:
      - /opt/deployments/prometheus.yml:/etc/prometheus/prometheus.yml - prod 
   #   - ./prometheus.yml:/etc/prometheus/prometheus.yml
    command:
      - --config.file=/etc/prometheus/prometheus.yml
    depends_on:
      - node-exporter  

  aptitest-uptime-kuma:
    image: louislam/uptime-kuma:1
    container_name: aptitest-uptime-kuma
    restart: always
    ports:
      - 9001:3051
    networks:
      - microservices-network         

  portainer:
    image: portainer/portainer-ce:latest
    container_name: aptitest-portainer
    restart: always
    ports:
      - 9000:9000
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock
      - portainer_data:/data
    networks:
      - microservices-network    

networks:
  microservices-network:
    driver: bridge  

volumes:
  portainer_data: