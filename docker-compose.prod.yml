networks:
  microservices-network:
    driver: bridge

services:
  api-gateway:
    image: ghcr.io/oyewolz/aptitest-api-gateway:latest
    ports:
      - "${GATEWAY_PORT}:${GATEWAY_PORT}"
    environment:
      - USER_SERVICE_HOST=${USER_SERVICE_HOST}
      - USER_SERVICE_PORT=${USER_SERVICE_PORT}
      - GATEWAY_PORT=${GATEWAY_PORT}
      - ASSESSMENT_ENGINE_MS_HOST=${ASSESSMENT_ENGINE_MS_HOST}
      - ASSESSMENT_ENGINE_MS_PORT=${ASSESSMENT_ENGINE_MS_PORT}
      - BLOB_MS_HOST=${BLOB_MS_HOST}
      - BLOB_MS_PORT=${BLOB_MS_PORT}
      - NOTIFICATION_MS_HOST=${NOTIFICATION_MS_HOST}
      - NOTIFICATION_MS_PORT=${NOTIFICATION_MS_PORT}
      - RABBITMQ_URI=${RABBITMQ_URI}
      - REDIS_HOST=${REDIS_HOST}
      - REDIS_PORT=${REDIS_PORT}
      - NODE_ENV=production
    depends_on:
      - user-service
    networks:
      - microservices-network

  user-service:
    image: ghcr.io/oyewolz/aptitest-user-service:latest
    ports:
      - "${USER_SERVICE_PORT}:${USER_SERVICE_PORT}"
    environment:
      - USER_SERVICE_PORT=${USER_SERVICE_PORT}
      - USER_SERVICE_HOST=${USER_SERVICE_HOST}
      - RABBITMQ_URI=${RABBITMQ_URI}
      - REDIS_HOST=${REDIS_HOST}
      - REDIS_PORT=${REDIS_PORT}
      - DB_TYPE=${DB_TYPE}
      - DB_HOST=${DB_HOST}
      - DB_PORT=${DB_PORT}
      - DB_USERNAME=${DB_USERNAME}
      - DB_PASSWORD=${DB_PASSWORD}
      - DB_DATABASE=${DB_DATABASE}
      - DB_POOL_MAX=${DB_POOL_MAX}
      - DB_POOL_MIN=${DB_POOL_MIN}
      - DB_POOL_IDLE_TIMEOUT=${DB_POOL_IDLE_TIMEOUT}
      - NODE_ENV=production
    networks:
      - microservices-network
  assessment-engine-ms:
    image: ghcr.io/oyewolz/aptitest-assessment-engine:latest
    ports:
      - "${ASSESSMENT_ENGINE_MS_PORT}:${ASSESSMENT_ENGINE_MS_PORT}"
    environment:
      - ASSESSMENT_ENGINE_MS_PORT=${ASSESSMENT_ENGINE_MS_PORT}
      - NODE_ENV=production
      - RABBITMQ_URI=${RABBITMQ_URI}
      - REDIS_HOST=${REDIS_HOST}
      - REDIS_PORT=${REDIS_PORT}
      - DB_HOST=${DB_HOST}
      - DB_PORT=${DB_PORT}
      - DB_USERNAME=${DB_USERNAME}
      - DB_PASSWORD=${DB_PASSWORD}
      - DB_DATABASE=${DB_DATABASE}
      - DB_TYPE=${DB_TYPE}
      - DB_POOL_MAX=${DB_POOL_MAX}
      - DB_POOL_MIN=${DB_POOL_MIN}
      - DB_POOL_IDLE_TIMEOUT=${DB_POOL_IDLE_TIMEOUT}
    networks:
      - microservices-network 

  notification-ms:
    image: ghcr.io/oyewolz/aptitest-notification-ms:latest
    ports:
      - "${NOTIFICATION_MS_PORT}:${NOTIFICATION_MS_PORT}"
    environment:
      - NOTIFICATION_MS_PORT=${NOTIFICATION_MS_PORT}
      - NODE_ENV=production
      - RABBITMQ_URI=${RABBITMQ_URI}
      - REDIS_HOST=${REDIS_HOST}
      - REDIS_PORT=${REDIS_PORT}
    networks:
      - microservices-network 


  

