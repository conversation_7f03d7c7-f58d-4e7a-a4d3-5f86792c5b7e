networks:
  microservices-network:
    driver: bridge

services:
  api-gateway:
    build:
      context: .
      dockerfile: Dockerfile.api-gateway
    command: npm run start:debug:api-gateway
    ports:
      - "${GATEWAY_PORT}:${GATEWAY_PORT}"
      - 9210:9210
    environment:
      - USER_SERVICE_HOST=${USER_SERVICE_HOST}
      - USER_SERVICE_PORT=${USER_SERVICE_PORT}
      - GATEWAY_PORT=${GATEWAY_PORT}
      - NODE_ENV=development
    depends_on:
      - user-service
    volumes:
      - .:/usr/src/app
      - ./logs:/usr/src/app/logs
    networks:
      - microservices-network

  user-service:
    build:
      context: .
      dockerfile: Dockerfile.user-service
    command: npm run start:debug:user-service
    ports:
      - "${USER_SERVICE_PORT}:${USER_SERVICE_PORT}"
      - 9229:9229
    environment:
      - USER_SERVICE_PORT=${USER_SERVICE_PORT}
      - USER_SERVICE_HOST=${USER_SERVICE_HOST}
      - NODE_ENV=development
    volumes:
      - .:/usr/src/app
      - ./logs:/usr/src/app/logs
    networks:
      - microservices-network
 
  blob-ms:
    build:
      context: .
      dockerfile: Dockerfile.blob-ms
    command: npm run start:debug:blob-ms
    ports:
      - "${BLOB_MS_PORT}:${BLOB_MS_PORT}"
      - 9230:9230
    environment:
      - BLOB_MS_PORT=${BLOB_MS_PORT}
      - NODE_ENV=development
    volumes:
      - .:/usr/src/app
      - ./logs:/usr/src/app/logs
    networks:
      - microservices-network
  assessment-engine-ms:
    build:
      context: .
      dockerfile: Dockerfile.assessment-engine
    command: npm run start:debug:assessment-engine-ms
    ports:
      - "${ASSESSMENT_ENGINE_MS_PORT}:${ASSESSMENT_ENGINE_MS_PORT}"
      - 9231:9231
    environment:
      - ASSESSMENT_ENGINE_MS_PORT=${ASSESSMENT_ENGINE_MS_PORT}
      - NODE_ENV=development
    volumes:
      - .:/usr/src/app
      - ./logs:/usr/src/app/logs
    networks:
      - microservices-network 

  notification-ms:
    build:
      context: .
      dockerfile: Dockerfile.notification-ms
    command: npm run start:debug:notification-ms
    ports:
      - "${NOTIFICATION_MS_PORT}:${NOTIFICATION_MS_PORT}"
      - 9232:9232
    environment:
      - NOTIFICATION_MS_PORT=${NOTIFICATION_MS_PORT}
      - NODE_ENV=development
    volumes:
      - .:/usr/src/app
      - ./logs:/usr/src/app/logs
    networks:
      - microservices-network


  

