name: Deploy to <PERSON><PERSON><PERSON>
on:
  workflow_run:
    workflows: ["Build and Publish Docker Images"]
    types:
      - completed

jobs:
  redeploy:
    name: Redeploy Portainer Stack
    runs-on: ubuntu-latest
    env:
      PORTAINER_URL: "${{ secrets.PORTAINER_URL }}"  # Or http://:9000 if not using HTTPS
    steps:
      - name: Fetch and Parse Stack Environment
        id: stack-env
        run: |
          ENV_RESPONSE=$(curl -s -k -H "X-API-Key: ${{ secrets.PORTAINER_API_KEY }}" \
            "${{ env.PORTAINER_URL }}/api/stacks/${{ secrets.PORTAINER_STACK_ID }}")
          
          if [ -z "$ENV_RESPONSE" ] || ! echo "$ENV_RESPONSE" | jq . > /dev/null 2>&1; then
            echo "ERROR: Failed to fetch or parse stack environment JSON"
            echo "Raw response: $ENV_RESPONSE"  # Log for debug
            exit 1
          fi
          
          # Set as compact JSON string (safe, no multiline issues)
          echo "stack_env_json<<EOF" >> $GITHUB_ENV
          echo "$ENV_RESPONSE" | jq -c . >> $GITHUB_ENV
          echo "" >> $GITHUB_ENV  # Ensure trailing newline for delimiter matching
          echo "<<EOF" >> $GITHUB_ENV

      - name: Fetch and Parse Stack File Content
        id: stack-file
        run: |
          FILE_RESPONSE=$(curl -s -k -H "X-API-Key: ${{ secrets.PORTAINER_API_KEY }}" \
            "${{ env.PORTAINER_URL }}/api/stacks/${{ secrets.PORTAINER_STACK_ID }}/file")
          
          if [ -z "$FILE_RESPONSE" ] || ! echo "$FILE_RESPONSE" | jq . > /dev/null 2>&1; then
            echo "ERROR: Failed to fetch or parse stack file JSON"
            echo "Raw response: $FILE_RESPONSE"  # Log for debug
            exit 1
          fi
          
          # Use unique delimiter for YAML content (timestamp-based, low conflict risk)
          UNIQUE_DELIM="DELIM_$(date +%s)_$(shuf -i 0-99999 -n 1)"  # Simple random for uniqueness (no uuid needed)
          echo "stack_file_content<<$UNIQUE_DELIM" >> $GITHUB_ENV
          echo "$FILE_RESPONSE" | jq -r '.StackFileContent' >> $GITHUB_ENV
          echo "" >> $GITHUB_ENV  # Ensure trailing newline for delimiter matching
          echo "$UNIQUE_DELIM" >> $GITHUB_ENV

      - name: Trigger Stack Redeploy
        run: |
          # Ensure jq is available (Ubuntu runner has it)
          jq --version || { echo "jq not found!"; exit 1; }
          
          # Extract from env (now safer)
          FULL_ENV_JSON=$(cat <<< "$stack_env_json")
          ENV_VARS=$(echo "$FULL_ENV_JSON" | jq -r '.Env')
          WEBHOOK=$(echo "$FULL_ENV_JSON" | jq -r '.Webhook // empty')
          
          # For file content, extract between delims (ignores extra newline)
          STACK_FILE_CONTENT=$(sed "1d" <<< "$stack_file_content" | sed "/$UNIQUE_DELIM/q" | head -n -1 | sed 's/[ \t]*$//' | tr -d '\n' '\n' | sed '$s/$/\n/')
          
          # Build payload (use compact JSON where possible; webhook as string)
          PAYLOAD=$(jq -n \
            --arg stackFile "$STACK_FILE_CONTENT" \
            --argjson env "$ENV_VARS" \
            --arg webhook "$WEBHOOK" \
            '{
              StackFileContent: $stackFile,
              Env: $env,
              Prune: false,
              PullImage: true,
              Webhook: $webhook,
              Id: ('${{ secrets.PORTAINER_STACK_ID }}' | tonumber)
            }' | jq -c .)  # Compact for curl; Id as number

          echo "Payload preview: $PAYLOAD"

          # Send update
          RESPONSE=$(curl -s -k -w "\n%{http_code}" -X PUT \
            -H "X-API-Key: ${{ secrets.PORTAINER_API_KEY }}" \
            -H "Content-Type: application/json" \
            -d "$PAYLOAD" \
            "${{ env.PORTAINER_URL }}/api/stacks/${{ secrets.PORTAINER_STACK_ID }}?endpointId=${{ secrets.PORTAINER_ENDPOINT_ID }}")

          HTTP_CODE=$(echo "$RESPONSE" | tail -1)
          BODY=$(echo "$RESPONSE" | sed '$d')  # Remove code line

          echo "Response code: $HTTP_CODE"
          echo "Response body: $BODY"
          
          if [ "$HTTP_CODE" != "200" ] && [ "$HTTP_CODE" != "202" ]; then
            echo "ERROR: Redeploy failed!"
            exit 1
          fi
          
          echo "Stack redeployed successfully!"