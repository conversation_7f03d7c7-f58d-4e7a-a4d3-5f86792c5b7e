name: Build and Publish Docker Images

on:
  push:
    branches:
      - main
  workflow_dispatch:

permissions:
  contents: read
  packages: write

env:
  REGISTRY: ghcr.io

jobs:
  build-and-push:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        include:
          - service: api-gateway
            dockerfile: Dockerfile.api-gateway
          - service: assessment-engine
            dockerfile: Dockerfile.assessment-engine
          - service: user-service
            dockerfile: Dockerfile.user-service
          - service: notification-ms
            dockerfile: Dockerfile.notification-ms
          - service: blob-ms
            dockerfile: Dockerfile.blob-ms
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Set lowercase repository owner
        id: lowercase
        run: echo "repo_owner=$(echo '${{github.repository_owner}}' | tr '[:upper:]' '[:lower:]')" >> $GITHUB_OUTPUT

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Log in to GitHub Container Registry
        uses: docker/login-action@v3
        with:
          registry: ${{ env.REGISTRY }}
          username:  ${{steps.lowercase.outputs.repo_owner}}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: Build and push ${{ matrix.service }} image
        uses: docker/build-push-action@v5
        with:
          context: .
          file: ${{ matrix.dockerfile }}
          push: true
          tags: |
            ${{ env.REGISTRY }}/${{steps.lowercase.outputs.repo_owner}}/aptitest-${{ matrix.service }}:latest
            ${{ env.REGISTRY }}/${{steps.lowercase.outputs.repo_owner}}/aptitest-${{ matrix.service }}:${{ github.sha }}
          labels: |
            org.opencontainers.image.source=${{ github.repository }}
            org.opencontainers.image.revision=${{ github.sha }}     
